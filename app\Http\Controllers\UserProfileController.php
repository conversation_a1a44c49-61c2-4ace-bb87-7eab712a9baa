<?php

namespace App\Http\Controllers;

use App\Services\FileStorageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserProfileController extends Controller
{
    /**
     * Show the user profile form.
     */
    public function show()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    /**
     * Update profile picture only.
     */
    public function updateProfilePicture(Request $request)
    {
        $user = Auth::user();

        // Handle delete profile picture request
        if ($request->has('delete_profile_picture')) {
            if ($user->profile_picture) {
                FileStorageService::deletePublicFile($user->profile_picture);
                $user->update(['profile_picture' => null]);
            }
            return back()->with('success', 'Foto profil berhasil dihapus!');
        }

        // Handle profile picture upload
        $request->validate([
            'profile_picture' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ], [
            'profile_picture.required' => 'Silakan pilih file gambar.',
            'profile_picture.image' => 'File harus berupa gambar.',
            'profile_picture.max' => 'Ukuran file maksimal 2MB.',
            'profile_picture.mimes' => 'Format file harus JPG, PNG, atau JPEG.',
        ]);

        // Delete old profile picture if exists
        if ($user->profile_picture) {
            FileStorageService::deletePublicFile($user->profile_picture);
        }

        $profilePicturePath = FileStorageService::storePublicUserFile(
            $request->file('profile_picture'),
            $user->id,
            'profile'
        );

        $user->update(['profile_picture' => $profilePicturePath]);

        return back()->with('success', 'Foto profil berhasil diperbarui!');
    }

    /**
     * Update the user profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'bio' => 'nullable|string|max:500',
            'location' => 'nullable|string|max:255',
            'job_title' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'linkedin_url' => 'nullable|url|max:255',
            'github_url' => 'nullable|url|max:255',
            'twitter_url' => 'nullable|url|max:255',
            'instagram_url' => 'nullable|url|max:255',
            'youtube_url' => 'nullable|url|max:255',
            'facebook_url' => 'nullable|url|max:255',
            'skills' => 'nullable|string|max:1000',
            // Education & Learning
            'pendidikan' => 'nullable|in:sma,diploma,s1,s2,s3',
            'institusi_pendidikan' => 'nullable|string|max:255',
            'jurusan' => 'nullable|string|max:255',
            'tahun_lulus' => 'nullable|integer|min:1950|max:2030',
            'minat_belajar' => 'nullable|array',
            'minat_belajar.*' => 'string|max:100',
            // Career Goals
            'short_term_goal' => 'nullable|string|max:1000',
            'long_term_goal' => 'nullable|string|max:1000',
            // Experience & Industry
            'experience_years' => 'nullable|integer|min:0|max:50',
            'industry_interests' => 'nullable|array',
            'industry_interests.*' => 'string|max:100',
            // Work & Learning Preferences
            'work_preferences' => 'nullable|array',
            'work_preferences.*' => 'string|max:100',
            'learning_preferences' => 'nullable|array',
            'learning_preferences.*' => 'string|max:100',
        ], [
            'name.required' => 'Nama wajib diisi.',
            'bio.max' => 'Bio maksimal 500 karakter.',
            'website.url' => 'Format website tidak valid.',
            'linkedin_url.url' => 'Format URL LinkedIn tidak valid.',
            'github_url.url' => 'Format URL GitHub tidak valid.',
            'twitter_url.url' => 'Format URL Twitter tidak valid.',
            'instagram_url.url' => 'Format URL Instagram tidak valid.',
            'youtube_url.url' => 'Format URL YouTube tidak valid.',
            'facebook_url.url' => 'Format URL Facebook tidak valid.',
            'pendidikan.in' => 'Pilih tingkat pendidikan yang valid.',
            'tahun_lulus.integer' => 'Tahun lulus harus berupa angka.',
            'tahun_lulus.min' => 'Tahun lulus minimal 1950.',
            'tahun_lulus.max' => 'Tahun lulus maksimal 2030.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->only([
            'name', 'bio', 'location', 'job_title', 'company', 'website',
            'linkedin_url', 'github_url', 'twitter_url', 'instagram_url',
            'youtube_url', 'facebook_url', 'pendidikan', 'institusi_pendidikan',
            'jurusan', 'tahun_lulus', 'experience_years'
        ]);

        // Handle skills as array
        if ($request->filled('skills')) {
            $skills = array_map('trim', explode(',', $request->skills));
            $data['skills'] = array_filter($skills); // Remove empty values
        } else {
            $data['skills'] = null;
        }

        // Handle minat_belajar as array
        if ($request->has('minat_belajar')) {
            $data['minat_belajar'] = $request->minat_belajar;
        } else {
            $data['minat_belajar'] = null;
        }

        // Handle career goals
        if ($request->filled('short_term_goal') || $request->filled('long_term_goal')) {
            $data['career_goals'] = [
                'short_term' => $request->short_term_goal,
                'long_term' => $request->long_term_goal,
            ];
        } else {
            $data['career_goals'] = null;
        }

        // Handle industry interests
        if ($request->has('industry_interests')) {
            $data['industry_interests'] = $request->industry_interests;
        } else {
            $data['industry_interests'] = null;
        }

        // Handle work preferences
        if ($request->has('work_preferences')) {
            $data['work_preferences'] = $request->work_preferences;
        } else {
            $data['work_preferences'] = null;
        }

        // Handle learning preferences
        if ($request->has('learning_preferences')) {
            $data['learning_preferences'] = $request->learning_preferences;
        } else {
            $data['learning_preferences'] = null;
        }

        $user->update($data);

        return back()->with('success', 'Profil berhasil diperbarui!');
    }

    /**
     * Delete profile picture.
     */
    public function deleteProfilePicture()
    {
        $user = Auth::user();

        if ($user->profile_picture) {
            FileStorageService::deletePublicFile($user->profile_picture);
            $user->update(['profile_picture' => null]);
        }

        return back()->with('success', 'Foto profil berhasil dihapus!');
    }

    /**
     * Show the change password form.
     */
    public function showChangePassword()
    {
        $user = Auth::user();
        return view('user.change-password', compact('user'));
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'current_password' => ['required', 'string', 'current_password:web'],
            'password' => [
                'required',
                'string',
                'min:8',
                'confirmed',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]*$/',
            ],
        ], [
            'current_password.required' => 'Password saat ini wajib diisi.',
            'current_password.current_password' => 'Password saat ini tidak sesuai.',
            'password.required' => 'Password baru wajib diisi.',
            'password.min' => 'Password minimal 8 karakter.',
            'password.confirmed' => 'Konfirmasi password tidak sesuai.',
            'password.regex' => 'Password harus mengandung minimal: 1 huruf kecil, 1 huruf besar, dan 1 angka.',
        ]);

        // Additional custom validation for more specific error messages
        if ($request->filled('password')) {
            $password = $request->password;
            $errors = [];

            if (strlen($password) < 8) {
                $errors[] = 'Password minimal 8 karakter.';
            }
            if (!preg_match('/[a-z]/', $password)) {
                $errors[] = 'Password harus mengandung minimal 1 huruf kecil (a-z).';
            }
            if (!preg_match('/[A-Z]/', $password)) {
                $errors[] = 'Password harus mengandung minimal 1 huruf besar (A-Z).';
            }
            if (!preg_match('/\d/', $password)) {
                $errors[] = 'Password harus mengandung minimal 1 angka (0-9).';
            }

            if (!empty($errors)) {
                return back()->withErrors([
                    'password' => $errors
                ], 'updatePassword')->withInput();
            }
        }

        if ($validator->fails()) {
            return back()->withErrors($validator, 'updatePassword')->withInput();
        }

        $user->forceFill([
            'password' => Hash::make($request->password),
        ])->save();

        return back()->with('success', 'Password berhasil diubah!');
    }
}
