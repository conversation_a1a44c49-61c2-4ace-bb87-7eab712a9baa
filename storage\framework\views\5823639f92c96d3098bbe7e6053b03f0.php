<?php $__env->startSection('title', $blogPost->meta_title ?: $blogPost->title); ?>
<?php $__env->startSection('meta_description', $blogPost->meta_description ?: $blogPost->excerpt); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Blog Show Mobile-First Responsive Styles */
.blog-show-page {
    min-height: 100vh;
    background: #f8fafc;
}

/* Breadcrumb */
.blog-breadcrumb {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 0;
}

@media (min-width: 768px) {
    .blog-breadcrumb {
        padding: 1.5rem 0;
    }
}

.blog-breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    flex-wrap: wrap;
}

.blog-breadcrumb-link {
    color: #6b7280;
    text-decoration: none;
    transition: color 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: 0.25rem 0;
}

.blog-breadcrumb-link:hover {
    color: #2563eb;
}

.blog-breadcrumb-current {
    color: #111827;
    font-weight: 500;
}

.blog-breadcrumb-icon {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

/* Article Header */
.blog-article-header {
    padding: 2rem 0;
}

@media (min-width: 768px) {
    .blog-article-header {
        padding: 2.5rem 0;
    }
}

.blog-article-container {
    max-width: 64rem;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .blog-article-container {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .blog-article-container {
        padding: 0 2rem;
    }
}

.blog-category-badge {
    background-color: #dbeafe;
    color: #1e40af;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 9999px;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 1rem;
}

.blog-article-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .blog-article-title {
        font-size: 2.25rem;
    }
}

@media (min-width: 1024px) {
    .blog-article-title {
        font-size: 2.5rem;
    }
}

.blog-article-meta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .blog-article-meta {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

.blog-author-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.blog-author-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    object-fit: cover;
}

.blog-author-details {
    flex: 1;
}

.blog-author-name {
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

.blog-article-stats {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    flex-wrap: wrap;
}

.blog-article-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .blog-article-actions {
        flex-direction: row;
        gap: 0.75rem;
    }
}

.blog-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-weight: 500;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 44px;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
}

.blog-action-btn-primary {
    background-color: #2563eb;
    color: white;
}

.blog-action-btn-primary:hover {
    background-color: #1d4ed8;
}

.blog-action-btn-saved {
    background-color: #059669;
    color: white;
}

.blog-action-btn-saved:hover {
    background-color: #047857;
}

.blog-action-btn-secondary {
    border: 1px solid #d1d5db;
    color: #374151;
    background-color: white;
}

.blog-action-btn-secondary:hover {
    background-color: #f9fafb;
}

.blog-featured-image {
    margin-bottom: 2rem;
}

.blog-featured-img {
    width: 100%;
    height: 16rem;
    object-fit: cover;
    border-radius: 0.5rem;
}

@media (min-width: 768px) {
    .blog-featured-img {
        height: 20rem;
    }
}

@media (min-width: 1024px) {
    .blog-featured-img {
        height: 24rem;
    }
}

/* Article Content */
.blog-article-content {
    margin-bottom: 2rem;
}

.blog-article-excerpt {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 2rem;
    font-weight: 500;
    line-height: 1.6;
}

@media (min-width: 768px) {
    .blog-article-excerpt {
        font-size: 1.25rem;
    }
}

.blog-content {
    line-height: 1.7;
    color: #374151;
    font-size: 1rem;
}

@media (min-width: 768px) {
    .blog-content {
        font-size: 1.125rem;
    }
}

/* Tags Section */
.blog-tags-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.blog-tags-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.blog-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.blog-tag {
    background-color: #f3f4f6;
    color: #374151;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 9999px;
    font-weight: 500;
}

/* Share Section */
.blog-share-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.blog-share-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #111827;
}

.blog-share-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

@media (min-width: 640px) {
    .blog-share-buttons {
        flex-direction: row;
        gap: 1rem;
    }
}

.blog-share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-weight: 500;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 44px;
    flex: 1;
}

@media (min-width: 640px) {
    .blog-share-btn {
        flex: none;
        min-width: 120px;
    }
}

.blog-share-btn-facebook {
    background-color: #2563eb;
    color: white;
}

.blog-share-btn-facebook:hover {
    background-color: #1d4ed8;
}

.blog-share-btn-twitter {
    background-color: #1f2937;
    color: white;
}

.blog-share-btn-twitter:hover {
    background-color: #111827;
}

.blog-share-btn-linkedin {
    background-color: #1e40af;
    color: white;
}

.blog-share-btn-linkedin:hover {
    background-color: #1e3a8a;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="blog-show-page">
    <!-- Breadcrumb -->
    <nav class="blog-breadcrumb">
        <div class="blog-article-container">
            <div class="blog-breadcrumb-nav">
                <a href="<?php echo e(route('home')); ?>" class="blog-breadcrumb-link">Home</a>
                <svg class="blog-breadcrumb-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="<?php echo e(route('blog.index')); ?>" class="blog-breadcrumb-link">Blog</a>
                <?php if($blogPost->category): ?>
                <svg class="blog-breadcrumb-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="<?php echo e(route('blog.category', $blogPost->category->slug)); ?>" class="blog-breadcrumb-link"><?php echo e($blogPost->category->name); ?></a>
                <?php endif; ?>
                <svg class="blog-breadcrumb-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="blog-breadcrumb-current"><?php echo e(Str::limit($blogPost->title, 30)); ?></span>
            </div>
        </div>
    </nav>

    <!-- Article Header -->
    <article class="blog-article-header">
        <div class="blog-article-container">
            <header>
                <?php if($blogPost->category): ?>
                <div>
                    <span class="blog-category-badge"><?php echo e($blogPost->category->name); ?></span>
                </div>
                <?php endif; ?>

                <h1 class="blog-article-title"><?php echo e($blogPost->title); ?></h1>

                <div class="blog-article-meta">
                    <div class="blog-author-info">
                        <img src="<?php echo e($blogPost->author->profile_picture ? asset('storage/' . $blogPost->author->profile_picture) : asset('images/avatars/placeholder.svg')); ?>"
                             alt="<?php echo e($blogPost->author->name); ?>" class="blog-author-avatar">
                        <div class="blog-author-details">
                            <p class="blog-author-name"><?php echo e($blogPost->author->name); ?></p>
                            <div class="blog-article-stats">
                                <span><?php echo e($blogPost->formatted_published_date); ?></span>
                                <span>•</span>
                                <span><?php echo e($blogPost->read_time_text); ?></span>
                                <span>•</span>
                                <span><?php echo e(number_format($blogPost->views_count)); ?> views</span>
                            </div>
                        </div>
                    </div>

                    <?php if(auth()->guard()->check()): ?>
                    <div class="blog-article-actions">
                        <button id="saveArticleBtn"
                                data-blog-slug="<?php echo e($blogPost->slug); ?>"
                                class="save-article-btn blog-action-btn <?php echo e($isSaved ? 'blog-action-btn-saved' : 'blog-action-btn-primary'); ?>">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <?php if($isSaved): ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                <?php else: ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                <?php endif; ?>
                            </svg>
                            <span class="save-text"><?php echo e($isSaved ? 'Tersimpan' : 'Simpan'); ?></span>
                        </button>

                        <a href="<?php echo e(route('user.blog')); ?>"
                           class="blog-action-btn blog-action-btn-secondary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0l-4 4m4-4l-4-4"></path>
                            </svg>
                            <span class="hidden sm:inline">Lihat Artikel Tersimpan</span>
                            <span class="sm:hidden">Tersimpan</span>
                        </a>
                    </div>
                    <?php endif; ?>
                </div>

                <?php if($blogPost->featured_image): ?>
                <div class="blog-featured-image">
                    <img src="<?php echo e(asset('storage/' . $blogPost->featured_image)); ?>"
                         alt="<?php echo e($blogPost->title); ?>"
                         class="blog-featured-img">
                </div>
                <?php endif; ?>
            </header>

            <!-- Article Content -->
            <div class="blog-article-content">
                <div class="blog-article-excerpt"><?php echo e($blogPost->excerpt); ?></div>

                <div class="blog-content">
                    <?php echo nl2br(e($blogPost->content)); ?>

                </div>
            </div>

            <!-- Tags -->
            <?php if($blogPost->tags && count($blogPost->tags) > 0): ?>
            <div class="blog-tags-section">
                <h3 class="blog-tags-title">Tags</h3>
                <div class="blog-tags-container">
                    <?php $__currentLoopData = $blogPost->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="blog-tag">#<?php echo e($tag); ?></span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Share Section -->
            <div class="blog-share-section">
                <h3 class="blog-share-title">Bagikan Artikel</h3>
                <div class="blog-share-buttons">
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(request()->url())); ?>"
                       target="_blank"
                       class="blog-share-btn blog-share-btn-facebook">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        <span class="hidden sm:inline">Facebook</span>
                    </a>

                    <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(request()->url())); ?>&text=<?php echo e(urlencode($blogPost->title)); ?>"
                       target="_blank"
                       class="blog-share-btn blog-share-btn-twitter">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        <span class="hidden sm:inline">Twitter</span>
                    </a>

                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(urlencode(request()->url())); ?>"
                       target="_blank"
                       class="blog-share-btn blog-share-btn-linkedin">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        <span class="hidden sm:inline">LinkedIn</span>
                    </a>
                </div>
            </div>
        </div>
    </article>

    <!-- Related Posts -->
    <?php if($relatedPosts->count() > 0): ?>
    <section class="py-12 bg-white border-t">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Artikel Terkait</h2>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $relatedPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <article class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                    <div class="relative">
                        <img src="<?php echo e($post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog/placeholder.svg')); ?>"
                             alt="<?php echo e($post->title); ?>" class="w-full h-48 object-cover">
                        <?php if($post->category): ?>
                        <span class="absolute top-3 left-3 bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"><?php echo e($post->category->name); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="p-6">
                        <div class="space-y-4">
                            <h3 class="text-lg font-semibold line-clamp-2">
                                <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="hover:text-blue-600 transition-colors">
                                    <?php echo e($post->title); ?>

                                </a>
                            </h3>

                            <p class="text-gray-600 text-sm line-clamp-3"><?php echo e($post->excerpt); ?></p>

                            <div class="flex items-center space-x-3">
                                <img src="<?php echo e($post->author->profile_picture ? asset('storage/' . $post->author->profile_picture) : asset('images/avatars/placeholder.svg')); ?>"
                                     alt="<?php echo e($post->author->name); ?>" class="w-8 h-8 rounded-full">
                                <div class="flex-1">
                                    <p class="text-sm font-medium"><?php echo e($post->author->name); ?></p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span><?php echo e($post->formatted_published_date); ?></span>
                                        <span>•</span>
                                        <span><?php echo e($post->read_time_text); ?></span>
                                    </div>
                                </div>
                            </div>

                            <?php if(auth()->guard()->check()): ?>
                            <div class="flex items-center space-x-2 pt-3 border-t border-gray-100">
                                <?php
                                    $isSaved = auth()->user()->savedBlogPosts()->where('blog_posts.id', $post->id)->exists();
                                ?>
                                <?php if($isSaved): ?>
                                    <button onclick="unsaveArticle('<?php echo e($post->slug); ?>')" class="flex-1 text-center px-3 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        Tersimpan
                                    </button>
                                <?php else: ?>
                                    <button onclick="saveArticle('<?php echo e($post->slug); ?>')" class="flex-1 text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                        </svg>
                                        Simpan
                                    </button>
                                <?php endif; ?>
                                <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="flex-1 text-center px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors">
                                    Baca
                                </a>
                            </div>
                            <?php else: ?>
                            <div class="pt-3 border-t border-gray-100">
                                <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="block w-full text-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                                    Baca Artikel
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
</div>

<?php if(auth()->guard()->check()): ?>
<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const saveBtn = document.getElementById('saveArticleBtn');
    if (saveBtn) {
        const blogSlug = saveBtn.getAttribute('data-blog-slug');
        const saveText = saveBtn.querySelector('.save-text');

        saveBtn.addEventListener('click', function() {
            toggleSaveArticle(blogSlug, saveBtn, saveText);
        });
    }

    function toggleSaveArticle(blogSlug, btn, textEl) {
        const isSaved = btn.classList.contains('bg-green-600');
        const url = `/blog/${blogSlug}/save`;
        const method = isSaved ? 'DELETE' : 'POST';

        btn.disabled = true;

        fetch(url, {
            method: method,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (isSaved) {
                    // Unsaved
                    btn.classList.remove('bg-green-600', 'hover:bg-green-700');
                    btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                    textEl.textContent = 'Simpan';
                    btn.querySelector('svg').innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>';
                } else {
                    // Saved
                    btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                    btn.classList.add('bg-green-600', 'hover:bg-green-700');
                    textEl.textContent = 'Tersimpan';
                    btn.querySelector('svg').innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>';
                }

                // Show success message
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message || 'Terjadi kesalahan', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Terjadi kesalahan saat menyimpan artikel', 'error');
        })
        .finally(() => {
            btn.disabled = false;
        });
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Global functions for related articles
    window.saveArticle = async function(articleSlug) {
        try {
            const response = await fetch(`/blog/${articleSlug}/save`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                showNotification('Artikel berhasil disimpan!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Gagal menyimpan artikel', 'error');
            }
        } catch (error) {
            console.error('Error saving article:', error);
            showNotification('Terjadi kesalahan saat menyimpan artikel', 'error');
        }
    };

    window.unsaveArticle = async function(articleSlug) {
        if (!confirm('Apakah Anda yakin ingin menghapus artikel ini dari daftar simpan?')) {
            return;
        }

        try {
            const response = await fetch(`/blog/${articleSlug}/save`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                showNotification('Artikel berhasil dihapus dari daftar simpan!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Gagal menghapus artikel', 'error');
            }
        } catch (error) {
            console.error('Error unsaving article:', error);
            showNotification('Terjadi kesalahan saat menghapus artikel', 'error');
        }
    };
});
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/blog/show.blade.php ENDPATH**/ ?>