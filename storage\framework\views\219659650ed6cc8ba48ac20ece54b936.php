<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Tutor Dashboard - Ngambiskuy'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- CSS -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Tutor Dashboard Responsive CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/tutor-dashboard-responsive.css')); ?>">

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo e(asset('images/logo.png')); ?>">

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-inter antialiased bg-gray-50 tutor-layout">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="hidden md:flex md:w-64 md:flex-col">
            <div class="flex flex-col flex-grow pt-5 overflow-y-auto bg-gradient-to-b from-emerald-50 to-teal-50 border-r border-emerald-200">
                <!-- Logo -->
                <div class="flex items-center flex-shrink-0 px-4">
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-lg flex items-center justify-center shadow-md">
                            <span class="text-white font-bold text-sm">N</span>
                        </div>
                        <span class="ml-2 text-xl font-bold text-gray-900">Ngambiskuy</span>
                        <span class="ml-2 text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full font-medium">Tutor</span>
                    </a>
                </div>

                <!-- User Info -->
                <div class="mt-6 px-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <img class="h-10 w-10 rounded-full" src="<?php echo e(Auth::user()->getProfilePictureUrl()); ?>" alt="<?php echo e(Auth::user()->name); ?>">
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900"><?php echo e(Auth::user()->name); ?></p>
                            <p class="text-xs text-gray-500"><?php echo e(Auth::user()->getRole()); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="mt-8 flex-1 px-2 space-y-1">
                    <a href="<?php echo e(route('tutor.dashboard')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.dashboard') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                        </svg>
                        Dashboard
                    </a>

                    <a href="<?php echo e(route('tutor.courses')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.courses') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Kursus Saya
                    </a>

                    <a href="<?php echo e(route('tutor.create-course')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.create-course') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Buat Kursus
                    </a>

                    <a href="<?php echo e(route('tutor.exams')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.exams*') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Ujian
                    </a>

                    <a href="<?php echo e(route('tutor.blogs')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.blogs*') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Blog
                    </a>

                    <a href="<?php echo e(route('tutor.students')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.students') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        Siswa
                    </a>

                    <a href="<?php echo e(route('tutor.analytics')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.analytics') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Analitik
                    </a>

                    <a href="<?php echo e(route('tutor.earnings')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.earnings') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        Penghasilan
                    </a>

                    <a href="<?php echo e(route('tutor.membership')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.membership') ? 'active' : ''); ?> <?php echo e(!auth()->user()->hasActiveMembership() ? 'bg-purple-50 border-purple-200 text-purple-700' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                        Membership
                        <?php if(!auth()->user()->hasActiveMembership()): ?>
                            <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                Upgrade
                            </span>
                        <?php endif; ?>
                    </a>

                    <?php if(!auth()->user()->hasActiveMembership()): ?>
                        <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
                            <div class="text-center">
                                <div class="w-6 h-6 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xs font-medium text-emerald-900 mb-1">NALA Membership</h4>
                                <p class="text-xs text-emerald-700 mb-2">Akses AI dan fitur premium</p>
                                <a href="<?php echo e(route('tutor.membership')); ?>" class="block w-full text-center px-2 py-1.5 bg-emerald-600 text-white text-xs font-medium rounded hover:bg-emerald-700 transition-colors">
                                    Upgrade Sekarang
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Divider -->
                    <div class="border-t border-gray-200 my-4"></div>

                    <a href="<?php echo e(route('tutor.settings')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.settings') ? 'active' : ''); ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Pengaturan
                    </a>

                    <!-- Divider -->
                    <div class="border-t border-emerald-200 my-4"></div>

                    <!-- Role Switcher -->
                    <div class="px-2 mb-4">
                        <div class="mb-2 px-1">
                            <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Switch Role</p>
                        </div>
                        <a href="<?php echo e(route('user.dashboard')); ?>" class="tutor-sidebar-link bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 hover:from-blue-100 hover:to-indigo-100 border border-blue-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <span class="flex-1">Dashboard Siswa</span>
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                Student
                            </span>
                        </a>
                    </div>

                    <a href="<?php echo e(route('home')); ?>" class="tutor-sidebar-link">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        Kembali ke Beranda
                    </a>
                </nav>

                <!-- Logout -->
                <div class="flex-shrink-0 p-4 border-t border-emerald-200">
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="tutor-sidebar-link w-full text-left text-red-600 hover:bg-red-50">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Mobile sidebar -->
        <div class="md:hidden" x-data="{ open: false }">
            <!-- Mobile menu button -->
            <div class="tutor-mobile-header fixed top-0 left-0 right-0 z-40 flex items-center justify-between h-16 px-4 bg-white border-b border-gray-200">
                <button @click="open = !open" class="hamburger-btn text-gray-500 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-lg flex items-center justify-center shadow-md">
                        <span class="text-white font-bold text-sm">N</span>
                    </div>
                    <span class="ml-2 text-lg font-bold text-gray-900">Ngambiskuy</span>
                    <span class="ml-2 text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full font-medium">Tutor</span>
                </div>
                <div class="w-10"></div> <!-- Spacer for balance -->
            </div>

            <!-- Mobile menu overlay -->
            <div x-show="open"
                 x-transition:enter="transition-opacity ease-linear duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 z-50 flex"
                 style="display: none;">
                <div @click="open = false" class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
                <div class="tutor-mobile-sidebar relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl">
                    <!-- Close button -->
                    <div class="absolute top-0 right-0 -mr-12 pt-2">
                        <button @click="open = false" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                            <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile sidebar content -->
                    <div class="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                        <div class="flex-shrink-0 flex items-center px-4">
                            <div class="w-8 h-8 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-lg flex items-center justify-center shadow-md">
                                <span class="text-white font-bold text-sm">N</span>
                            </div>
                            <span class="ml-2 text-xl font-bold text-gray-900">Ngambiskuy</span>
                            <span class="ml-2 text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full font-medium">Tutor</span>
                        </div>

                        <!-- User Info Mobile -->
                        <div class="mt-6 px-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <img class="h-10 w-10 rounded-full" src="<?php echo e(Auth::user()->getProfilePictureUrl()); ?>" alt="<?php echo e(Auth::user()->name); ?>">
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900"><?php echo e(Auth::user()->name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e(Auth::user()->getRole()); ?></p>
                                </div>
                            </div>
                        </div>

                        <nav class="mt-8 px-2 space-y-1">
                            <a href="<?php echo e(route('tutor.dashboard')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.dashboard') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                                </svg>
                                Dashboard
                            </a>

                            <a href="<?php echo e(route('tutor.courses')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.courses') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                Kursus Saya
                            </a>

                            <a href="<?php echo e(route('tutor.create-course')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.create-course') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Buat Kursus
                            </a>

                            <a href="<?php echo e(route('tutor.exams')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.exams*') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Ujian
                            </a>

                            <a href="<?php echo e(route('tutor.blogs')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.blogs*') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Blog
                            </a>

                            <a href="<?php echo e(route('tutor.students')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.students') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                Siswa
                            </a>

                            <a href="<?php echo e(route('tutor.analytics')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.analytics') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Analitik
                            </a>

                            <a href="<?php echo e(route('tutor.earnings')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.earnings') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                Penghasilan
                            </a>

                            <a href="<?php echo e(route('tutor.membership')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.membership') ? 'active' : ''); ?> <?php echo e(!auth()->user()->hasActiveMembership() ? 'bg-purple-50 border-purple-200 text-purple-700' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                                Membership
                                <?php if(!auth()->user()->hasActiveMembership()): ?>
                                    <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                        Upgrade
                                    </span>
                                <?php endif; ?>
                            </a>

                            <?php if(!auth()->user()->hasActiveMembership()): ?>
                                <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
                                    <div class="text-center">
                                        <div class="w-6 h-6 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                            </svg>
                                        </div>
                                        <h4 class="text-xs font-medium text-emerald-900 mb-1">NALA Membership</h4>
                                        <p class="text-xs text-emerald-700 mb-2">Akses AI dan fitur premium</p>
                                        <a href="<?php echo e(route('tutor.membership')); ?>" class="block w-full text-center px-2 py-1.5 bg-emerald-600 text-white text-xs font-medium rounded hover:bg-emerald-700 transition-colors" @click="open = false">
                                            Upgrade Sekarang
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Divider -->
                            <div class="border-t border-gray-200 my-4"></div>

                            <a href="<?php echo e(route('tutor.settings')); ?>" class="tutor-sidebar-link <?php echo e(request()->routeIs('tutor.settings') ? 'active' : ''); ?>" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Pengaturan
                            </a>

                            <!-- Role Switcher Mobile -->
                            <div class="border-t border-emerald-200 pt-4 mt-4">
                                <div class="mb-2 px-3">
                                    <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Switch Role</p>
                                </div>
                                <a href="<?php echo e(route('user.dashboard')); ?>" class="tutor-sidebar-link bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 hover:from-blue-100 hover:to-indigo-100 border border-blue-200" @click="open = false">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    <span class="flex-1">Dashboard Siswa</span>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Student
                                    </span>
                                </a>
                            </div>

                            <a href="<?php echo e(route('home')); ?>" class="tutor-sidebar-link" @click="open = false">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                                </svg>
                                Kembali ke Beranda
                            </a>

                            <!-- Logout Mobile -->
                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="tutor-sidebar-link w-full text-left text-red-600 hover:bg-red-50">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Logout
                                </button>
                            </form>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Top bar for mobile -->
            <div class="md:hidden h-16"></div>

            <!-- Page content -->
            <main class="tutor-dashboard-container flex-1 relative overflow-y-auto focus:outline-none">
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <!-- AI Chat Component -->
    <?php echo $__env->make('components.ai-chat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/layouts/tutor.blade.php ENDPATH**/ ?>