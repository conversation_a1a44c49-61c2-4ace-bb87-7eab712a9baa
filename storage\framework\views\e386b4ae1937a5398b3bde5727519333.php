<?php $__env->startSection('title', $seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="description" content="<?php echo e($seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.'); ?>">
<meta name="keywords" content="<?php echo e($seoData['keywords'] ?? 'kursus programming, belajar coding, kursus teknologi'); ?>">
<meta name="robots" content="index, follow">
<link rel="canonical" href="<?php echo e($seoData['canonical'] ?? request()->url()); ?>">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="<?php echo e(request()->url()); ?>">
<meta property="og:title" content="<?php echo e($seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy'); ?>">
<meta property="og:description" content="<?php echo e($seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.'); ?>">
<meta property="og:image" content="<?php echo e(asset('images/og-courses.jpg')); ?>">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="<?php echo e(request()->url()); ?>">
<meta property="twitter:title" content="<?php echo e($seoData['title'] ?? 'Kursus Teknologi Terbaik - Ngambiskuy'); ?>">
<meta property="twitter:description" content="<?php echo e($seoData['description'] ?? 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development.'); ?>">
<meta property="twitter:image" content="<?php echo e(asset('images/og-courses.jpg')); ?>">

<!-- Structured Data -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "EducationalOrganization",
  "name": "Ngambiskuy",
  "description": "Platform pembelajaran teknologi bertenaga AI untuk mengembangkan skill programming dan teknologi",
  "url": "<?php echo e(url('/')); ?>",
  "logo": "<?php echo e(asset('images/logo.png')); ?>",
  "courseMode": "online",
  "numberOfCourses": <?php echo e($stats['total_courses']); ?>,
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": <?php echo e(number_format($stats['average_rating'], 1)); ?>,
    "ratingCount": <?php echo e($stats['total_students']); ?>

  }
}
</script>

<style>
.filter-sidebar {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-right: 1px solid #e2e8f0;
}

.course-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e2e8f0;
}

.course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #FF6B35;
}

.price-range-slider {
    background: linear-gradient(to right, #FF6B35, #E55A2B);
}

.filter-chip {
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    color: white;
    border-radius: 20px;
    padding: 6px 16px;
    font-size: 14px;
    font-weight: 500;
}

.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.hero-gradient {
    background: linear-gradient(135deg, #FF6B35 0%, #E55A2B 50%, #FF8C42 100%);
}

.search-input {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #FF6B35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

@media (max-width: 768px) {
    .filter-sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        z-index: 50;
        transition: left 0.3s ease;
    }
    
    .filter-sidebar.active {
        left: 0;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-gradient py-16 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl lg:text-5xl font-bold mb-6">
                Kursus Teknologi
                <span class="text-yellow-200">Terdepan</span>
            </h1>
            <p class="text-xl text-white/90 leading-relaxed max-w-3xl mx-auto mb-8">
                Kuasai skill teknologi masa depan dengan <?php echo e(number_format($stats['total_courses'])); ?> kursus berkualitas tinggi.
                Bergabung dengan <?php echo e(number_format($stats['total_students'])); ?> siswa yang telah mengembangkan karir mereka.
            </p>
            
            <!-- Quick Stats -->
            
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Sidebar Filters -->
            <aside class="lg:w-80 filter-sidebar p-6 rounded-xl h-fit lg:sticky lg:top-8">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-900">Filter Kursus</h3>
                    <button class="lg:hidden text-gray-500 hover:text-gray-700" onclick="toggleMobileFilters()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form method="GET" action="<?php echo e(route('courses.index')); ?>" class="space-y-6">
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Cari Kursus</label>
                        <div class="relative">
                            <input type="text" 
                                   name="search" 
                                   value="<?php echo e(request('search')); ?>"
                                   placeholder="Masukkan kata kunci..."
                                   class="search-input w-full pl-10 pr-4 py-3 text-sm focus:outline-none">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Kategori</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="category" value="" <?php echo e(!request('category') ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">Semua Kategori</span>
                            </label>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="<?php echo e($category->slug); ?>" <?php echo e(request('category') == $category->slug ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm text-gray-700"><?php echo e($category->name); ?></span>
                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Level Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Level Kesulitan</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="level" value="" <?php echo e(!request('level') ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">Semua Level</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="beginner" <?php echo e(request('level') == 'beginner' ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">🟢 Pemula</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="intermediate" <?php echo e(request('level') == 'intermediate' ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">🟡 Menengah</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="advanced" <?php echo e(request('level') == 'advanced' ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">🔴 Lanjutan</span>
                            </label>
                        </div>
                    </div>

                    <!-- Price Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Harga</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="price_type" value="" <?php echo e(!request('price_type') ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">Semua Harga</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="price_type" value="free" <?php echo e(request('price_type') == 'free' ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">💚 Gratis</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="price_type" value="paid" <?php echo e(request('price_type') == 'paid' ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                <span class="ml-2 text-sm text-gray-700">💎 Premium</span>
                            </label>
                        </div>
                    </div>

                    <!-- Rating Filter -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-3">Rating Minimum</label>
                        <div class="space-y-2">
                            <?php for($i = 4; $i >= 3; $i--): ?>
                                <label class="flex items-center">
                                    <input type="radio" name="rating" value="<?php echo e($i); ?>" <?php echo e(request('rating') == $i ? 'checked' : ''); ?> class="text-primary focus:ring-primary">
                                    <span class="ml-2 flex items-center">
                                        <?php for($j = 1; $j <= 5; $j++): ?>
                                            <svg class="w-4 h-4 <?php echo e($j <= $i ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        <?php endfor; ?>
                                        <span class="ml-1 text-sm text-gray-600">& ke atas</span>
                                    </span>
                                </label>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3 pt-4 border-t border-gray-200">
                        <button type="submit" class="w-full btn btn-primary py-3 text-sm font-semibold">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Terapkan Filter
                        </button>
                        <?php if(request()->hasAny(['search', 'category', 'level', 'price_type', 'rating'])): ?>
                            <a href="<?php echo e(route('courses.index')); ?>" class="w-full btn btn-outline py-3 text-sm font-semibold text-center block">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Reset Filter
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </aside>

            <!-- Main Content Area -->
            <main class="flex-1">
                <!-- Mobile Filter Toggle -->
                <div class="lg:hidden mb-6">
                    <button onclick="toggleMobileFilters()" class="flex items-center justify-center w-full py-3 px-4 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                        </svg>
                        Filter & Urutkan
                    </button>
                </div>

                <?php if($courses->count() > 0): ?>
                    <!-- Results Header -->
                    <div class="bg-white rounded-xl p-6 mb-6 shadow-sm border border-gray-200">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                                    <?php echo e(number_format($courses->total())); ?> Kursus Ditemukan
                                </h2>
                                <p class="text-gray-600">
                                    <?php if(request()->hasAny(['search', 'category', 'level', 'price_type', 'rating'])): ?>
                                        Hasil pencarian untuk filter yang dipilih
                                    <?php else: ?>
                                        Semua kursus tersedia di platform kami
                                    <?php endif; ?>
                                </p>

                                <!-- Active Filters -->
                                <?php if(request()->hasAny(['search', 'category', 'level', 'price_type', 'rating'])): ?>
                                    <div class="flex flex-wrap gap-2 mt-3">
                                        <?php if(request('search')): ?>
                                            <span class="filter-chip">
                                                Pencarian: "<?php echo e(request('search')); ?>"
                                                <a href="<?php echo e(request()->fullUrlWithQuery(['search' => null])); ?>" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        <?php endif; ?>
                                        <?php if(request('category')): ?>
                                            <?php $category = $categories->where('slug', request('category'))->first(); ?>
                                            <?php if($category): ?>
                                                <span class="filter-chip">
                                                    <?php echo e($category->name); ?>

                                                    <a href="<?php echo e(request()->fullUrlWithQuery(['category' => null])); ?>" class="ml-2 text-white/80 hover:text-white">×</a>
                                                </span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <?php if(request('level')): ?>
                                            <span class="filter-chip">
                                                Level: <?php echo e(ucfirst(request('level'))); ?>

                                                <a href="<?php echo e(request()->fullUrlWithQuery(['level' => null])); ?>" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        <?php endif; ?>
                                        <?php if(request('price_type')): ?>
                                            <span class="filter-chip">
                                                <?php echo e(request('price_type') == 'free' ? 'Gratis' : 'Premium'); ?>

                                                <a href="<?php echo e(request()->fullUrlWithQuery(['price_type' => null])); ?>" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        <?php endif; ?>
                                        <?php if(request('rating')): ?>
                                            <span class="filter-chip">
                                                Rating <?php echo e(request('rating')); ?>+ ⭐
                                                <a href="<?php echo e(request()->fullUrlWithQuery(['rating' => null])); ?>" class="ml-2 text-white/80 hover:text-white">×</a>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Sort Options -->
                            <div class="flex items-center gap-3">
                                <label class="text-sm font-medium text-gray-700">Urutkan:</label>
                                <select onchange="window.location.href=this.value" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                                    <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'newest'])); ?>" <?php echo e(request('sort') == 'newest' || !request('sort') ? 'selected' : ''); ?>>Terbaru</option>
                                    <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'popular'])); ?>" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Terpopuler</option>
                                    <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'rating'])); ?>" <?php echo e(request('sort') == 'rating' ? 'selected' : ''); ?>>Rating Tertinggi</option>
                                    <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_low'])); ?>" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Harga Terendah</option>
                                    <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'price_high'])); ?>" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Harga Tertinggi</option>
                                    <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'duration_short'])); ?>" <?php echo e(request('sort') == 'duration_short' ? 'selected' : ''); ?>>Durasi Terpendek</option>
                                    <option value="<?php echo e(request()->fullUrlWithQuery(['sort' => 'duration_long'])); ?>" <?php echo e(request('sort') == 'duration_long' ? 'selected' : ''); ?>>Durasi Terpanjang</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Courses Grid -->
                    <div class="grid md:grid-cols-2 xl:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="course-card bg-white rounded-xl overflow-hidden group">
                                <div class="relative">
                                    <?php if($course->thumbnail): ?>
                                        <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                             alt="<?php echo e($course->title); ?>"
                                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500">
                                    <?php else: ?>
                                        <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                            <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Badges -->
                                    <div class="absolute top-3 left-3 flex flex-col gap-2">
                                        <?php if($course->is_featured): ?>
                                            <span class="bg-yellow-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                                                ⭐ FEATURED
                                            </span>
                                        <?php endif; ?>
                                        <?php if($course->price == 0): ?>
                                            <span class="bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-full">
                                                💚 GRATIS
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Price Badge -->
                                    <?php if($course->price > 0): ?>
                                        <div class="absolute top-3 right-3 bg-primary text-white text-sm font-bold px-3 py-2 rounded-lg shadow-lg">
                                            <?php echo e($course->formatted_price); ?>

                                        </div>
                                    <?php endif; ?>


                                </div>

                                <div class="p-6">
                                    <!-- Category & Level -->
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="inline-block bg-primary/10 text-primary text-xs font-semibold px-3 py-1 rounded-full">
                                            <?php echo e($course->category->name); ?>

                                        </span>
                                        <span class="text-xs font-semibold px-2 py-1 rounded-full
                                            <?php echo e($course->level == 'beginner' ? 'bg-green-100 text-green-800' : ''); ?>

                                            <?php echo e($course->level == 'intermediate' ? 'bg-yellow-100 text-yellow-800' : ''); ?>

                                            <?php echo e($course->level == 'advanced' ? 'bg-red-100 text-red-800' : ''); ?>">
                                            <?php echo e($course->level_indonesian); ?>

                                        </span>
                                    </div>

                                    <!-- Title -->
                                    <h3 class="text-lg font-bold text-gray-900 line-clamp-2 mb-3 group-hover:text-primary transition-colors">
                                        <?php echo e($course->title); ?>

                                    </h3>

                                    <!-- Description -->
                                    <p class="text-gray-600 text-sm line-clamp-2 leading-relaxed mb-4">
                                        <?php echo e($course->description); ?>

                                    </p>

                                    <!-- Instructor -->
                                    <div class="flex items-center space-x-3 mb-4">
                                        <div class="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm font-semibold text-gray-900"><?php echo e($course->tutor->name); ?></p>
                                            <p class="text-xs text-gray-500">Instruktur</p>
                                        </div>
                                    </div>

                                    <!-- Course Stats -->
                                    <div class="grid grid-cols-2 gap-4 py-3 mb-4 border-t border-gray-100">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-sm text-gray-600"><?php echo e($course->duration ?? '4 jam'); ?></span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                            </svg>
                                            <span class="text-sm text-gray-600"><?php echo e(number_format($course->total_students ?? 0)); ?> siswa</span>
                                        </div>
                                    </div>

                                    <!-- Rating -->
                                    <?php if(($course->average_rating ?? 0) > 0): ?>
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center space-x-2">
                                                <div class="flex items-center">
                                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                                        <svg class="w-4 h-4 <?php echo e($i <= ($course->average_rating ?? 0) ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                        </svg>
                                                    <?php endfor; ?>
                                                </div>
                                                <span class="text-sm font-semibold text-gray-900"><?php echo e(number_format($course->average_rating ?? 0, 1)); ?></span>
                                            </div>
                                            <span class="text-xs text-gray-500">(<?php echo e(number_format($course->total_reviews ?? 0)); ?> ulasan)</span>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Action Button -->
                                    <div class="pt-2">
                                        <?php if(auth()->guard()->check()): ?>
                                            <?php
                                                // Check if user is the course tutor
                                                $isTutor = $course->tutor_id === auth()->id();
                                                
                                                // Check if user is enrolled in this course (including free course access)
                                                $isEnrolled = auth()->user()->enrollments()
                                                    ->where('course_id', $course->id)
                                                    ->where('status', 'active')
                                                    ->exists();
                                                
                                                // Check if course is free
                                                $isFree = $course->is_free || $course->price == 0;
                                                
                                                // For free courses, check if user has started learning (has any progress)
                                                $hasStartedLearning = false;
                                                if ($isFree && !$isEnrolled) {
                                                    // Check if user has any lesson progress for this course
                                                    $hasStartedLearning = \App\Models\LessonProgress::where('user_id', auth()->id())
                                                        ->whereHas('lesson.chapter', function($q) use ($course) {
                                                            $q->where('course_id', $course->id);
                                                        })
                                                        ->exists();
                                                }
                                            ?>
                                            
                                            <?php if($isTutor): ?>
                                                <a href="<?php echo e(route('tutor.curriculum.index', $course)); ?>" class="btn btn-outline w-full transition-colors py-3 font-semibold">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    Kelola Kursus
                                                </a>
                                            <?php elseif($isEnrolled || $hasStartedLearning): ?>
                                                <a href="<?php echo e(route('course.learn', $course)); ?>" class="btn btn-primary w-full group-hover:bg-primary-dark transition-colors py-3 font-semibold">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    Lanjutkan Belajar
                                                </a>
                                            <?php elseif($isFree): ?>
                                                <a href="<?php echo e(route('course.learn', $course)); ?>" class="btn btn-success w-full group-hover:bg-green-600 transition-colors py-3 font-semibold">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    Mulai Belajar Gratis
                                                </a>
                                            <?php else: ?>
                                                <a href="<?php echo e(route('course.show', $course)); ?>" class="btn btn-primary w-full group-hover:bg-primary-dark transition-colors py-3 font-semibold">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                    </svg>
                                                    <?php if($course->price == 0): ?>
                                                        Mulai Belajar
                                                    <?php else: ?>
                                                        Daftar Sekarang
                                                    <?php endif; ?>
                                                </a>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <a href="<?php echo e(route('course.show', $course)); ?>" class="btn btn-primary w-full group-hover:bg-primary-dark transition-colors py-3 font-semibold">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                Lihat Detail
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </article>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-12">
                        <div class="flex justify-center">
                            <?php echo e($courses->links()); ?>

                        </div>
                    </div>
                <?php else: ?>
                    <!-- No Courses Found -->
                    <div class="bg-white rounded-xl p-12 text-center shadow-sm border border-gray-200">
                        <div class="max-w-md mx-auto">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-3">Tidak Ada Kursus Ditemukan</h3>
                            <p class="text-gray-600 mb-8 leading-relaxed">
                                Maaf, tidak ada kursus yang sesuai dengan kriteria pencarian Anda.
                                Coba ubah filter atau kata kunci untuk menemukan kursus yang tepat.
                            </p>
                            <div class="space-y-3">
                                <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary btn-lg">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Lihat Semua Kursus
                                </a>
                                <div class="text-sm text-gray-500">
                                    atau <a href="<?php echo e(route('courses.index', ['price_type' => 'free'])); ?>" class="text-primary hover:underline">coba kursus gratis</a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="hero-gradient py-20 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="max-w-3xl mx-auto">
            <h2 class="text-3xl lg:text-4xl font-bold mb-6">
                Siap Memulai Perjalanan Belajar Anda?
            </h2>
            <p class="text-xl text-white/90 mb-8 leading-relaxed">
                Bergabunglah dengan <?php echo e(number_format($stats['total_students'])); ?> profesional yang telah mengembangkan karir mereka bersama Ngambiskuy.
                Mulai hari ini dan raih masa depan yang lebih cerah!
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="<?php echo e(route('register')); ?>" class="btn btn-lg bg-white text-primary hover:bg-gray-100 font-semibold">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Daftar Sekarang
                </a>
                <a href="<?php echo e(route('courses.index', ['price_type' => 'free'])); ?>" class="btn btn-lg border-2 border-white text-white hover:bg-white hover:text-primary font-semibold">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Coba Gratis
                </a>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script>
function toggleMobileFilters() {
    const sidebar = document.querySelector('.filter-sidebar');
    sidebar.classList.toggle('active');
}

// Close mobile filters when clicking outside
document.addEventListener('click', function(event) {
    const sidebar = document.querySelector('.filter-sidebar');
    const toggleButton = document.querySelector('[onclick="toggleMobileFilters()"]');

    if (window.innerWidth < 1024 &&
        !sidebar.contains(event.target) &&
        !toggleButton.contains(event.target) &&
        sidebar.classList.contains('active')) {
        sidebar.classList.remove('active');
    }
});

// Auto-submit form when radio buttons change
document.querySelectorAll('input[type="radio"]').forEach(radio => {
    radio.addEventListener('change', function() {
        // Add a small delay to improve UX
        setTimeout(() => {
            this.closest('form').submit();
        }, 100);
    });
});

// Smooth scroll to courses section when clicking hero CTA
document.addEventListener('DOMContentLoaded', function() {
    const ctaButtons = document.querySelectorAll('a[href="#courses"]');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector('#courses') || document.querySelector('main');
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});

// Add loading state to filter form
document.querySelector('form').addEventListener('submit', function() {
    const submitButton = this.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Mencari...';
        submitButton.disabled = true;
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/courses-v2.blade.php ENDPATH**/ ?>