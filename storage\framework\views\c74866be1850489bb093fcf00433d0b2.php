<?php $__env->startSection('title', '<PERSON><PERSON>ku<PERSON>'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/dashboard-responsive.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="dashboard-container p-3 sm:p-4 lg:p-6 bg-gray-50 min-h-screen">
    <!-- Page Header -->
    <div class="mb-6 lg:mb-8">
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <div class="text-center sm:text-left">
                <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">Profil Saya</h1>
                <p class="text-gray-600 mt-1 text-sm sm:text-base">Kelola informasi profil dan preferensi akun <PERSON></p>
            </div>
        </div>
    </div>

    <div class="space-y-6 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-6">
        <!-- Profile Picture & Basic Info -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
                <div class="text-center">
                    <div class="relative inline-block">
                        <?php if($user->profile_picture): ?>
                            <img src="<?php echo e($user->getProfilePictureUrl()); ?>" alt="Profile" class="w-20 h-20 sm:w-24 sm:h-24 rounded-full object-cover mx-auto mb-4 border-4 border-gray-200">
                        <?php else: ?>
                            <div class="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-primary to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-10 h-10 sm:w-12 sm:h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        <?php endif; ?>
                    </div>
                    <h3 class="text-base sm:text-lg font-bold text-gray-900"><?php echo e($user->name); ?></h3>
                    <p class="text-xs sm:text-sm text-gray-600 break-all"><?php echo e($user->email); ?></p>
                    <div class="mt-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Akun Terverifikasi</span>
                        </span>
                    </div>

                    <!-- Profile Picture Upload Form -->
                    <form action="<?php echo e(route('user.profile.picture.update')); ?>" method="POST" enctype="multipart/form-data" class="mt-4">
                        <?php echo csrf_field(); ?>
                        <div class="space-y-2">
                            <input type="file" id="profile_picture" name="profile_picture" accept="image/jpeg,image/png,image/jpg" class="hidden" onchange="this.form.submit()">
                            <label for="profile_picture" class="btn btn-outline w-full min-h-[44px] cursor-pointer text-xs sm:text-sm justify-center">
                                <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span>Ubah Foto Profil</span>
                            </label>
                            <?php if($user->profile_picture): ?>
                                <button type="submit" name="delete_profile_picture" value="1" class="btn btn-outline w-full min-h-[44px] text-red-600 hover:bg-red-50 text-xs sm:text-sm justify-center">
                                    <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    <span>Hapus Foto</span>
                                </button>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 mt-4 sm:mt-5 lg:mt-6">
                <h4 class="font-semibold text-gray-900 mb-4 text-sm sm:text-base text-center sm:text-left">Statistik Belajar</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-xs sm:text-sm text-gray-600">Kursus Diikuti</span>
                        <span class="font-medium text-gray-900 text-sm sm:text-base">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs sm:text-sm text-gray-600">Sertifikat</span>
                        <span class="font-medium text-gray-900 text-sm sm:text-base">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs sm:text-sm text-gray-600">Poin XP</span>
                        <span class="font-medium text-gray-900 text-sm sm:text-base">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs sm:text-sm text-gray-600">Bergabung</span>
                        <span class="font-medium text-gray-900 text-sm sm:text-base"><?php echo e($user->created_at->format('M Y')); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
                <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-4 sm:mb-6 text-center sm:text-left">Informasi Profil</h3>

                <form action="<?php echo e(route('user.profile.update')); ?>" method="POST" class="space-y-4 sm:space-y-6">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <?php if(session('success')): ?>
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg text-sm sm:text-base">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if($errors->any()): ?>
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm sm:text-base">
                            <ul class="list-disc list-inside">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                        <div>
                            <label for="name" class="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Nama Lengkap</label>
                            <input type="text" id="name" name="name" value="<?php echo e(old('name', $user->name)); ?>"
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm sm:text-base min-h-[44px] <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs sm:text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="email" class="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="email" name="email" value="<?php echo e($user->email); ?>" readonly
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 text-sm sm:text-base min-h-[44px]">
                            <p class="text-xs text-gray-500 mt-1">Email tidak dapat diubah</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                        <div>
                            <label for="job_title" class="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Pekerjaan</label>
                            <input type="text" id="job_title" name="job_title" value="<?php echo e(old('job_title', $user->job_title)); ?>" placeholder="Mahasiswa, Developer, Designer, dll."
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm sm:text-base min-h-[44px] <?php $__errorArgs = ['job_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['job_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs sm:text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="company" class="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Perusahaan</label>
                            <input type="text" id="company" name="company" value="<?php echo e(old('company', $user->company)); ?>" placeholder="Nama perusahaan/institusi"
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm sm:text-base min-h-[44px] <?php $__errorArgs = ['company'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['company'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs sm:text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div>
                        <label for="bio" class="block text-xs sm:text-sm font-medium text-gray-700 mb-2">Bio</label>
                        <textarea id="bio" name="bio" rows="4" placeholder="Ceritakan sedikit tentang diri Anda..."
                                  class="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm sm:text-base <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('bio', $user->bio)); ?></textarea>
                        <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-xs sm:text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Lokasi</label>
                        <input type="text" id="location" name="location" value="<?php echo e(old('location', $user->location)); ?>" placeholder="Kota, Provinsi"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <?php $__errorArgs = ['location'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="pendidikan" class="block text-sm font-medium text-gray-700 mb-2">Pendidikan</label>
                            <select id="pendidikan" name="pendidikan"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['pendidikan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="">Pilih tingkat pendidikan</option>
                                <option value="sma" <?php echo e(old('pendidikan', $user->pendidikan) == 'sma' ? 'selected' : ''); ?>>SMA/SMK</option>
                                <option value="diploma" <?php echo e(old('pendidikan', $user->pendidikan) == 'diploma' ? 'selected' : ''); ?>>Diploma</option>
                                <option value="s1" <?php echo e(old('pendidikan', $user->pendidikan) == 's1' ? 'selected' : ''); ?>>Sarjana (S1)</option>
                                <option value="s2" <?php echo e(old('pendidikan', $user->pendidikan) == 's2' ? 'selected' : ''); ?>>Magister (S2)</option>
                                <option value="s3" <?php echo e(old('pendidikan', $user->pendidikan) == 's3' ? 'selected' : ''); ?>>Doktor (S3)</option>
                            </select>
                            <?php $__errorArgs = ['pendidikan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="institusi_pendidikan" class="block text-sm font-medium text-gray-700 mb-2">Institusi Pendidikan</label>
                            <input type="text" id="institusi_pendidikan" name="institusi_pendidikan" value="<?php echo e(old('institusi_pendidikan', $user->institusi_pendidikan)); ?>" placeholder="Universitas/Sekolah"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['institusi_pendidikan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['institusi_pendidikan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="jurusan" class="block text-sm font-medium text-gray-700 mb-2">Jurusan</label>
                            <input type="text" id="jurusan" name="jurusan" value="<?php echo e(old('jurusan', $user->jurusan)); ?>" placeholder="Teknik Informatika, Manajemen, dll."
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['jurusan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['jurusan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="tahun_lulus" class="block text-sm font-medium text-gray-700 mb-2">Tahun Lulus</label>
                            <input type="number" id="tahun_lulus" name="tahun_lulus" value="<?php echo e(old('tahun_lulus', $user->tahun_lulus)); ?>" placeholder="2024" min="1950" max="2030"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['tahun_lulus'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <?php $__errorArgs = ['tahun_lulus'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Minat Belajar</label>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                            <?php
                                $interests = ['Programming', 'Data Science', 'Design', 'Mobile Dev', 'AI/ML', 'Business', 'Marketing', 'Finance', 'UI/UX', 'DevOps', 'Cybersecurity', 'Blockchain'];
                                $userInterests = old('minat_belajar', $user->minat_belajar ?? []);
                            ?>
                            <?php $__currentLoopData = $interests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $interest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="flex items-center">
                                    <input type="checkbox" name="minat_belajar[]" value="<?php echo e($interest); ?>"
                                           <?php echo e(in_array($interest, $userInterests) ? 'checked' : ''); ?>

                                           class="rounded border-gray-300 text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm text-gray-700"><?php echo e($interest); ?></span>
                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <?php $__errorArgs = ['minat_belajar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Career Goals Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Tujuan Karir</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="short_term_goal" class="block text-sm font-medium text-gray-700 mb-2">Tujuan Jangka Pendek (1-2 tahun)</label>
                                <textarea id="short_term_goal" name="short_term_goal" rows="3" placeholder="Contoh: Menguasai React.js, Mendapat sertifikasi AWS, dll."
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['short_term_goal'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('short_term_goal', $user->career_goals['short_term'] ?? '')); ?></textarea>
                                <?php $__errorArgs = ['short_term_goal'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label for="long_term_goal" class="block text-sm font-medium text-gray-700 mb-2">Tujuan Jangka Panjang (3-5 tahun)</label>
                                <textarea id="long_term_goal" name="long_term_goal" rows="3" placeholder="Contoh: Menjadi Tech Lead, Memulai startup, dll."
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['long_term_goal'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('long_term_goal', $user->career_goals['long_term'] ?? '')); ?></textarea>
                                <?php $__errorArgs = ['long_term_goal'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Experience & Industry Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Pengalaman & Industri</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="experience_years" class="block text-sm font-medium text-gray-700 mb-2">Tahun Pengalaman Kerja</label>
                                <select id="experience_years" name="experience_years"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent <?php $__errorArgs = ['experience_years'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                    <option value="">Pilih pengalaman</option>
                                    <option value="0" <?php echo e(old('experience_years', $user->experience_years) == '0' ? 'selected' : ''); ?>>Fresh Graduate / Belum Bekerja</option>
                                    <option value="1" <?php echo e(old('experience_years', $user->experience_years) == '1' ? 'selected' : ''); ?>>1 Tahun</option>
                                    <option value="2" <?php echo e(old('experience_years', $user->experience_years) == '2' ? 'selected' : ''); ?>>2 Tahun</option>
                                    <option value="3" <?php echo e(old('experience_years', $user->experience_years) == '3' ? 'selected' : ''); ?>>3 Tahun</option>
                                    <option value="4" <?php echo e(old('experience_years', $user->experience_years) == '4' ? 'selected' : ''); ?>>4 Tahun</option>
                                    <option value="5" <?php echo e(old('experience_years', $user->experience_years) == '5' ? 'selected' : ''); ?>>5 Tahun</option>
                                    <option value="6" <?php echo e(old('experience_years', $user->experience_years) == '6' ? 'selected' : ''); ?>>6-10 Tahun</option>
                                    <option value="10" <?php echo e(old('experience_years', $user->experience_years) == '10' ? 'selected' : ''); ?>>10+ Tahun</option>
                                </select>
                                <?php $__errorArgs = ['experience_years'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Industri yang Diminati</label>
                                <div class="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-3">
                                    <?php
                                        $industries = ['Technology', 'Finance', 'Healthcare', 'Education', 'E-commerce', 'Gaming', 'Startups', 'Government', 'Manufacturing', 'Media', 'Consulting', 'Real Estate'];
                                        $userIndustries = old('industry_interests', $user->industry_interests ?? []);
                                    ?>
                                    <?php $__currentLoopData = $industries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $industry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="industry_interests[]" value="<?php echo e($industry); ?>"
                                                   <?php echo e(in_array($industry, $userIndustries) ? 'checked' : ''); ?>

                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700"><?php echo e($industry); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <?php $__errorArgs = ['industry_interests'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Work Preferences Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Preferensi Kerja</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Mode Kerja</label>
                                <div class="space-y-2">
                                    <?php
                                        $workModes = ['remote' => 'Remote', 'hybrid' => 'Hybrid', 'onsite' => 'On-site'];
                                        $userWorkPrefs = old('work_preferences', $user->work_preferences ?? []);
                                    ?>
                                    <?php $__currentLoopData = $workModes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="work_preferences[]" value="<?php echo e($value); ?>"
                                                   <?php echo e(in_array($value, $userWorkPrefs) ? 'checked' : ''); ?>

                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700"><?php echo e($label); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <?php $__errorArgs = ['work_preferences'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Preferensi Lainnya</label>
                                <div class="space-y-2">
                                    <?php
                                        $otherPrefs = ['flexible_hours' => 'Jam Kerja Fleksibel', 'team_collaboration' => 'Kerja Tim', 'leadership' => 'Posisi Leadership', 'mentoring' => 'Mentoring', 'learning_opportunities' => 'Kesempatan Belajar', 'creative_freedom' => 'Kebebasan Kreatif'];
                                    ?>
                                    <?php $__currentLoopData = $otherPrefs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="work_preferences[]" value="<?php echo e($value); ?>"
                                                   <?php echo e(in_array($value, $userWorkPrefs) ? 'checked' : ''); ?>

                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700"><?php echo e($label); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Preferences Section -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Preferensi Belajar</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Gaya Belajar</label>
                                <div class="space-y-2">
                                    <?php
                                        $learningStyles = ['visual' => 'Visual (Video, Diagram)', 'auditory' => 'Auditory (Audio, Diskusi)', 'kinesthetic' => 'Kinesthetic (Praktek Langsung)', 'reading' => 'Reading (Teks, Artikel)'];
                                        $userLearningPrefs = old('learning_preferences', $user->learning_preferences ?? []);
                                    ?>
                                    <?php $__currentLoopData = $learningStyles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="learning_preferences[]" value="<?php echo e($value); ?>"
                                                   <?php echo e(in_array($value, $userLearningPrefs) ? 'checked' : ''); ?>

                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700"><?php echo e($label); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <?php $__errorArgs = ['learning_preferences'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-sm mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Waktu Belajar Ideal</label>
                                <div class="space-y-2">
                                    <?php
                                        $learningTimes = ['morning' => 'Pagi (06:00-12:00)', 'afternoon' => 'Siang (12:00-18:00)', 'evening' => 'Malam (18:00-24:00)', 'flexible' => 'Fleksibel'];
                                    ?>
                                    <?php $__currentLoopData = $learningTimes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <label class="flex items-center">
                                            <input type="checkbox" name="learning_preferences[]" value="<?php echo e($value); ?>"
                                                   <?php echo e(in_array($value, $userLearningPrefs) ? 'checked' : ''); ?>

                                                   class="rounded border-gray-300 text-primary focus:ring-primary">
                                            <span class="ml-2 text-sm text-gray-700"><?php echo e($label); ?></span>
                                        </label>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button type="button" class="btn btn-outline">
                            Batal
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Simpan Perubahan
                        </button>
                    </div>
                </form>
                </div>

            <!-- Security Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Keamanan Akun</h3>

                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-900">Password</h4>
                            <p class="text-sm text-gray-600">Terakhir diubah <?php echo e($user->updated_at->format('d M Y')); ?></p>
                        </div>
                        <a href="<?php echo e(route('user.change-password')); ?>" class="btn btn-outline btn-sm">
                            Ubah Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/user/profile.blade.php ENDPATH**/ ?>